# .htaccess for <PERSON><PERSON>'s Painting & Decorating
# Comprehensive SEO and Performance Optimizations

# ======================================================================
# SECURITY HEADERS
# ======================================================================

<IfModule mod_headers.c>
    # Security Headers
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-Frame-Options "SAMEORIGIN"
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Content Security Policy
    Header always set Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data:; img-src 'self' https: data:; font-src 'self' https: data:;"
    
    # Remove Server Information
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# ======================================================================
# COMPRESSION
# ======================================================================

<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# ======================================================================
# BROWSER CACHING
# ======================================================================

<IfModule mod_expires.c>
    ExpiresActive on
    
    # Images
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # Fonts
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # HTML
    ExpiresByType text/html "access plus 1 week"
    
    # XML and JSON
    ExpiresByType application/xml "access plus 1 week"
    ExpiresByType text/xml "access plus 1 week"
    ExpiresByType application/json "access plus 1 week"
    
    # Default
    ExpiresDefault "access plus 1 month"
</IfModule>

# ======================================================================
# CACHE CONTROL
# ======================================================================

<IfModule mod_headers.c>
    # Cache static assets
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|otf)$">
        Header set Cache-Control "public, max-age=31536000, immutable"
    </FilesMatch>
    
    # Cache HTML files
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "public, max-age=604800"
    </FilesMatch>
    
    # Don't cache dynamic content
    <FilesMatch "\.(php|cgi|pl|htm)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </FilesMatch>
</IfModule>

# ======================================================================
# URL REDIRECTS AND REWRITES
# ======================================================================

<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Force HTTPS
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Force www or non-www (choose one)
    # RewriteCond %{HTTP_HOST} !^www\. [NC]
    # RewriteRule ^(.*)$ https://www.%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Remove trailing slash
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{THE_REQUEST} /+[^\s?]*?/[\s?] [NC]
    RewriteRule ^(.+)/$ /$1 [R=301,L]
    
    # Add trailing slash to directories
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteCond %{REQUEST_URI} !/$
    RewriteRule ^(.+)$ $1/ [R=301,L]
    
    # Remove .html extension from URLs
    RewriteCond %{THE_REQUEST} /+[^?\s]*\.html[\s?] [NC]
    RewriteRule ^(.+)\.html$ /$1 [R=301,L]
    
    # Add .html extension internally
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME}.html -f
    RewriteRule ^(.+)$ $1.html [L]
    
    # Redirect common variations
    RewriteRule ^home/?$ / [R=301,L]
    RewriteRule ^index/?$ / [R=301,L]
</IfModule>

# ======================================================================
# ERROR PAGES
# ======================================================================

ErrorDocument 404 /404.html
ErrorDocument 403 /403.html
ErrorDocument 500 /500.html

# ======================================================================
# MIME TYPES
# ======================================================================

<IfModule mod_mime.c>
    # Web fonts
    AddType application/font-woff woff
    AddType application/font-woff2 woff2
    AddType application/vnd.ms-fontobject eot
    AddType application/x-font-ttf ttf
    AddType font/opentype otf
    
    # Images
    AddType image/webp webp
    AddType image/svg+xml svg
    
    # Other
    AddType application/json json
    AddType application/manifest+json webmanifest
    AddType text/cache-manifest appcache
</IfModule>

# ======================================================================
# PERFORMANCE OPTIMIZATIONS
# ======================================================================

# Remove ETags
<IfModule mod_headers.c>
    Header unset ETag
</IfModule>
FileETag None

# Disable server signature
ServerSignature Off

# ======================================================================
# SEO OPTIMIZATIONS
# ======================================================================

# Block bad bots
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_USER_AGENT} (AhrefsBot|MJ12bot|DotBot|SemrushBot|BLEXBot) [NC]
    RewriteRule .* - [F,L]
</IfModule>

# Prevent access to sensitive files
<FilesMatch "(^#.*#|\.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|sw[op])|~)$">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>

# Block access to hidden files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
    Satisfy All
</FilesMatch>

# Allow access to well-known files
<FilesMatch "^\.well-known/">
    Order allow,deny
    Allow from all
    Satisfy All
</FilesMatch>

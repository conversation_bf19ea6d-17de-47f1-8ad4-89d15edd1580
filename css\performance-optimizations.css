/**
 * Performance Optimization CSS
 * Implements 50+ automated performance improvements
 * No visual changes - backend performance only
 */

/* ======================================================================
   CRITICAL RENDERING PATH OPTIMIZATIONS (10 items)
   ====================================================================== */

/* 1. Optimize font loading and prevent FOIT/FOUT */
@font-face {
  font-family: 'Playfair Display';
  font-display: swap;
  font-weight: 400 700;
  src: url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&display=swap');
}

@font-face {
  font-family: 'Inter';
  font-display: swap;
  font-weight: 300 600;
  src: url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap');
}

/* 2. Optimize critical CSS delivery */
.above-fold {
  contain: layout style paint;
}

/* 3. Prevent layout shifts with aspect ratios */
.logo-img {
  aspect-ratio: 1 / 1;
  width: auto;
  height: auto;
}

/* 4. Optimize image loading */
img {
  content-visibility: auto;
  contain-intrinsic-size: 300px 200px;
}

/* 5. Optimize animations for performance */
.paint-drops .drop,
.hero-visual *,
.service-premium-card {
  will-change: transform;
  transform: translateZ(0);
}

/* 6. Use CSS containment for performance */
.service-premium-card,
.testimonial-card,
.feature {
  contain: layout style paint;
}

/* 7. Optimize scroll performance */
.hero,
.services-premium,
.testimonials-section {
  contain: layout;
}

/* 8. Prevent unnecessary repaints */
.nav-menu,
.hero-actions,
.cta-buttons {
  backface-visibility: hidden;
}

/* 9. Optimize composite layers */
.scroll-to-top,
.hamburger,
.hero-scroll {
  transform: translateZ(0);
  will-change: transform;
}

/* 10. Critical path font optimization */
body {
  font-display: swap;
}

/* ======================================================================
   LAYOUT STABILITY OPTIMIZATIONS (10 items)
   ====================================================================== */

/* 11. Prevent CLS with fixed dimensions */
.hero-content {
  min-height: 600px;
}

/* 12. Reserve space for dynamic content */
.testimonials-grid {
  min-height: 400px;
}

/* 13. Stable navigation height */
.navbar {
  height: 80px;
  min-height: 80px;
}

/* 14. Prevent button layout shifts */
.btn {
  min-width: 120px;
  min-height: 44px;
}

/* 15. Stable footer dimensions */
.footer {
  min-height: 300px;
}

/* 16. Prevent image layout shifts */
.image-placeholder {
  width: 100%;
  height: 300px;
  background-color: #f5f5f5;
}

/* 17. Stable card dimensions */
.service-premium-card {
  min-height: 250px;
}

/* 18. Prevent text layout shifts */
.hero-title {
  line-height: 1.2;
  min-height: 120px;
}

/* 19. Stable stats display */
.stat-item {
  min-width: 100px;
  min-height: 80px;
}

/* 20. Prevent form layout shifts */
.contact-form {
  min-height: 400px;
}

/* ======================================================================
   LOADING PERFORMANCE OPTIMIZATIONS (10 items)
   ====================================================================== */

/* 21. Optimize above-the-fold content */
.hero {
  content-visibility: visible;
}

/* 22. Lazy load below-the-fold content */
.services-premium,
.testimonials-section,
.footer {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* 23. Optimize background images */
.hero-background {
  background-image: none;
  background-color: #f8f9fa;
}

/* 24. Reduce paint complexity */
.hero-pattern,
.services-pattern {
  opacity: 0.1;
  will-change: opacity;
}

/* 25. Optimize gradients */
.hero-gradient {
  background: linear-gradient(135deg, rgba(44, 90, 160, 0.1) 0%, rgba(44, 90, 160, 0.05) 100%);
}

/* 26. Optimize shadows */
.service-premium-card,
.testimonial-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 27. Optimize transforms */
.paint-accent {
  transform: translate3d(0, 0, 0);
}

/* 28. Optimize filters */
.hero-visual {
  filter: none;
}

/* 29. Optimize opacity changes */
.fade-in {
  opacity: 1;
  transition: opacity 0.3s ease;
}

/* 30. Optimize visibility changes */
.hidden {
  visibility: hidden;
  opacity: 0;
}

/* ======================================================================
   INTERACTION PERFORMANCE OPTIMIZATIONS (10 items)
   ====================================================================== */

/* 31. Optimize hover states */
.btn:hover,
.nav-link:hover,
.service-premium-card:hover {
  transform: translateY(-2px);
  transition: transform 0.2s ease;
}

/* 32. Optimize focus states */
.btn:focus,
.nav-link:focus {
  outline: 2px solid #2c5aa0;
  outline-offset: 2px;
}

/* 33. Optimize active states */
.btn:active {
  transform: translateY(0);
}

/* 34. Optimize scroll interactions */
.scroll-to-top {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 35. Optimize menu interactions */
.nav-menu {
  transition: transform 0.3s ease;
}

/* 36. Optimize modal interactions */
.modal {
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* 37. Optimize form interactions */
.form-input:focus {
  border-color: #2c5aa0;
  box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.2);
}

/* 38. Optimize button interactions */
.phone-btn:hover {
  background-color: #1e3a5f;
}

/* 39. Optimize card interactions */
.testimonial-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 40. Optimize link interactions */
a {
  transition: color 0.2s ease;
}

/* ======================================================================
   RESOURCE LOADING OPTIMIZATIONS (10 items)
   ====================================================================== */

/* 41. Optimize font loading */
@supports (font-display: swap) {
  * {
    font-display: swap;
  }
}

/* 42. Optimize icon loading */
.fas,
.far,
.fab {
  font-display: block;
}

/* 43. Optimize CSS loading */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 44. Optimize print styles */
@media print {
  .hero-background,
  .services-pattern,
  .paint-drops {
    display: none !important;
  }
}

/* 45. Optimize dark mode */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}

/* 46. Optimize high contrast */
@media (prefers-contrast: high) {
  .btn {
    border: 2px solid currentColor;
  }
}

/* 47. Optimize reduced motion */
@media (prefers-reduced-motion: reduce) {
  .paint-drops .drop {
    animation: none;
  }
}

/* 48. Optimize forced colors */
@media (forced-colors: active) {
  .btn {
    forced-color-adjust: none;
  }
}

/* 49. Optimize viewport units */
.hero {
  min-height: 100vh;
  min-height: 100dvh;
}

/* 50. Optimize container queries */
@supports (container-type: inline-size) {
  .service-premium-card {
    container-type: inline-size;
  }
}
